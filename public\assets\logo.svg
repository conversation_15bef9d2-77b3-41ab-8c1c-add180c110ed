<svg version="1.1" id="filingCabinetLogo" xmlns="http://www.w3.org/2000/svg" 
     xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 64 64" xml:space="preserve">
  <defs>
    <!-- Gradient for the cabinet body -->
    <linearGradient id="cabinetGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
    <!-- Drop shadow filter -->
    <filter id="shadowFilter" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="#000" flood-opacity="0.2"/>
    </filter>
  </defs>
  <style type="text/css">
    /* Cabinet with gradient, white outline, and drop shadow */
    .cabinet { 
      fill: url(#cabinetGradient); 
      stroke: white; 
      stroke-width: 2; 
    }
    .divider { 
      stroke: #1565C0; 
      stroke-width: 1.5; 
    }
    .drawer { 
      fill: #FFFFFF; 
    }
    .handle { 
      fill: #1565C0; 
    }
  </style>
  <!-- Cabinet Body with rounded corners, white outline, and drop shadow -->
  <rect x="4" y="4" width="56" height="56" rx="6" ry="6" class="cabinet" filter="url(#shadowFilter)"/>
  <!-- Divider lines for drawers -->
  <line x1="5" y1="22" x2="59" y2="22" class="divider"/>
  <line x1="5" y1="34" x2="59" y2="34" class="divider"/>
  <!-- Drawers with Handles -->
  <rect x="8" y="24" width="48" height="6" rx="1" ry="1" class="drawer"/>
  <circle cx="54" cy="27" r="1.5" class="handle"/>
  
  <rect x="8" y="36" width="48" height="6" rx="1" ry="1" class="drawer"/>
  <circle cx="54" cy="39" r="1.5" class="handle"/>
  
  <rect x="8" y="48" width="48" height="6" rx="1" ry="1" class="drawer"/>
  <circle cx="54" cy="51" r="1.5" class="handle"/>
  
  <!-- Additional detail: a small top handle on the cabinet door -->
  <rect x="28" y="10" width="8" height="4" rx="1" ry="1" fill="#1565C0"/>
</svg>
