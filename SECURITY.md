# Security Policy

## Supported Versions

FileRise is actively maintained. Only supported versions will receive security updates. For details on which versions are currently supported, please see the [Release Notes](https://github.com/error311/FileRise/releases).

## Reporting a Vulnerability

If you discover a security vulnerability, please do not open a public issue. Instead, follow these steps:

1. **Email Us Privately:**  
   Send an email to [<EMAIL>](mailto:<EMAIL>) with the subject line “[FileRise] Security Vulnerability Report”.

2. **Include Details:**  
   Provide a detailed description of the vulnerability, steps to reproduce it, and any other relevant information (e.g., affected versions, screenshots, logs).

3. **Secure Communication (Optional):**  
   If you wish to discuss the vulnerability securely, you can use our PGP key. You can obtain our PGP key by emailing us, and we will send it upon request.

## Disclosure Policy

- **Acknowledgement:**  
  We will acknowledge receipt of your report within 48 hours.
  
- **Resolution Timeline:**  
  We aim to fix confirmed vulnerabilities within 30 days. In cases where a delay is necessary, we will communicate updates to you directly.

- **Public Disclosure:**  
  After a fix is available, details of the vulnerability will be disclosed publicly in a way that does not compromise user security.

## Additional Information

We appreciate responsible disclosure of vulnerabilities and thank all researchers who help keep FileRise secure. For any questions related to this policy, please contact us at [<EMAIL>](mailto:<EMAIL>).
